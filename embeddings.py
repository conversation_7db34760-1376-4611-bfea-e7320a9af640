from google.cloud import aiplatform
from firebase_admin import firestore
from config import Config
import json
import logging
import numpy as np
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)


class EmbeddingService:
    def __init__(self):
        """Initialize the embedding service with Vertex AI"""
        try:
            aiplatform.init(project=Config.PROJECT_ID,
                            location=Config.LOCATION)
            self.db = firestore.client()
            logger.info("EmbeddingService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize EmbeddingService: {e}")
            raise

    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for given text using Vertex AI"""
        try:
            # Use the text-multilingual-embedding-002 model
            model = aiplatform.TextEmbeddingModel.from_pretrained(
                Config.EMBEDDING_MODEL)
            embeddings = model.get_embeddings([text])
            return embeddings[0].values
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise

    def store_restaurant_embeddings(self, restaurant_id: str) -> bool:
        """Create and store embeddings for a restaurant"""
        try:
            doc_ref = self.db.collection('restaurants').document(restaurant_id)
            doc = doc_ref.get()

            if not doc.exists:
                raise ValueError(f'Restaurant {restaurant_id} not found')

            data = doc.to_dict()

            # Create comprehensive text for embedding
            embedding_text = self._create_restaurant_embedding_text(data)

            # Generate embedding
            embedding_vector = self.generate_embedding(embedding_text)

            # Store embedding in Firestore
            doc_ref.update({
                'embedding': embedding_vector,
                'embedding_text': embedding_text,
                'embedding_updated': firestore.SERVER_TIMESTAMP
            })

            logger.info(f"Restaurant embedding stored for {restaurant_id}")
            return True

        except Exception as e:
            logger.error(
                f"Failed to store restaurant embedding for {restaurant_id}: {e}")
            raise

    def store_client_embeddings(self, client_id: str) -> bool:
        """Create and store embeddings for a client"""
        try:
            doc_ref = self.db.collection('clients').document(client_id)
            doc = doc_ref.get()

            if not doc.exists:
                raise ValueError(f'Client {client_id} not found')

            data = doc.to_dict()

            # Create comprehensive text for embedding
            embedding_text = self._create_client_embedding_text(data)

            # Generate embedding
            embedding_vector = self.generate_embedding(embedding_text)

            # Store embedding in Firestore
            doc_ref.update({
                'embedding': embedding_vector,
                'embedding_text': embedding_text,
                'embedding_updated': firestore.SERVER_TIMESTAMP
            })

            logger.info(f"Client embedding stored for {client_id}")
            return True

        except Exception as e:
            logger.error(
                f"Failed to store client embedding for {client_id}: {e}")
            raise

    def store_menu_embeddings(self, restaurant_id: str, menu_id: str) -> bool:
        """Create and store embeddings for a menu item"""
        try:
            doc_ref = self.db.collection('restaurants').document(
                restaurant_id).collection('menu').document(menu_id)
            doc = doc_ref.get()

            if not doc.exists:
                raise ValueError(
                    f'Menu item {menu_id} not found in restaurant {restaurant_id}')

            data = doc.to_dict()

            # Create comprehensive text for embedding
            embedding_text = self._create_menu_embedding_text(data)

            # Generate embedding
            embedding_vector = self.generate_embedding(embedding_text)

            # Store embedding in Firestore
            doc_ref.update({
                'embedding': embedding_vector,
                'embedding_text': embedding_text,
                'embedding_updated': firestore.SERVER_TIMESTAMP
            })

            logger.info(f"Menu embedding stored for {restaurant_id}/{menu_id}")
            return True

        except Exception as e:
            logger.error(
                f"Failed to store menu embedding for {restaurant_id}/{menu_id}: {e}")
            raise

    def _create_restaurant_embedding_text(self, data: Dict[str, Any]) -> str:
        """Create comprehensive text for restaurant embedding"""
        parts = []

        # Basic info
        if 'restaurantName' in data:
            parts.append(f"Restaurant: {data['restaurantName']}")

        if 'description' in data:
            parts.append(f"Description: {data['description']}")

        if 'cuisine' in data:
            parts.append(f"Cuisine: {data['cuisine']}")

        if 'city' in data:
            parts.append(f"Location: {data['city']}")

        if 'address' in data:
            parts.append(f"Address: {data['address']}")

        # Features and amenities
        if 'features' in data and isinstance(data['features'], list):
            parts.append(f"Features: {', '.join(data['features'])}")

        # Price range
        if 'priceRange' in data:
            parts.append(f"Price range: {data['priceRange']}")

        # Dietary options
        if 'dietaryOptions' in data and isinstance(data['dietaryOptions'], list):
            parts.append(
                f"Dietary options: {', '.join(data['dietaryOptions'])}")

        return ' | '.join(parts)

    def _create_client_embedding_text(self, data: Dict[str, Any]) -> str:
        """Create comprehensive text for client embedding"""
        parts = []

        # Basic info
        if 'name' in data:
            parts.append(f"Name: {data['name']}")

        # Meal preferences
        if 'mealPreferences' in data:
            prefs = data['mealPreferences']

            if 'dietaryRestrictions' in prefs and isinstance(prefs['dietaryRestrictions'], list):
                parts.append(
                    f"Dietary restrictions: {', '.join(prefs['dietaryRestrictions'])}")

            if 'allergies' in prefs and isinstance(prefs['allergies'], list):
                parts.append(f"Allergies: {', '.join(prefs['allergies'])}")

            if 'favoriteCuisines' in prefs and isinstance(prefs['favoriteCuisines'], list):
                parts.append(
                    f"Favorite cuisines: {', '.join(prefs['favoriteCuisines'])}")

        # Dietary goals
        if 'dietaryGoals' in data:
            goals = data['dietaryGoals']
            if isinstance(goals, dict):
                if 'dailyCalories' in goals:
                    parts.append(
                        f"Daily calorie goal: {goals['dailyCalories']}")
                if 'goal' in goals:
                    parts.append(f"Goal: {goals['goal']}")
            elif isinstance(goals, list):
                parts.append(f"Goals: {', '.join(map(str, goals))}")

        return ' | '.join(parts)

    def _create_menu_embedding_text(self, data: Dict[str, Any]) -> str:
        """Create comprehensive text for menu item embedding"""
        parts = []

        # Basic info
        if 'name' in data:
            parts.append(f"Dish: {data['name']}")

        if 'description' in data:
            parts.append(f"Description: {data['description']}")

        if 'category' in data:
            parts.append(f"Category: {data['category']}")

        # Nutritional info
        if 'calories' in data:
            parts.append(f"Calories: {data['calories']}")

        if 'protein' in data:
            parts.append(f"Protein: {data['protein']}g")

        if 'carbs' in data:
            parts.append(f"Carbs: {data['carbs']}g")

        if 'fat' in data:
            parts.append(f"Fat: {data['fat']}g")

        # Dietary info
        if 'dietary' in data and isinstance(data['dietary'], list):
            parts.append(f"Dietary: {', '.join(data['dietary'])}")

        if 'allergens' in data and isinstance(data['allergens'], list):
            parts.append(f"Allergens: {', '.join(data['allergens'])}")

        # Price
        if 'price' in data:
            parts.append(f"Price: {data['price']}")

        return ' | '.join(parts)

    def batch_create_embeddings(self, batch_type: str = 'all') -> Dict[str, Any]:
        """Batch create embeddings for multiple documents"""
        results = {
            'restaurants': {'success': 0, 'failed': 0, 'errors': []},
            'clients': {'success': 0, 'failed': 0, 'errors': []},
            'menus': {'success': 0, 'failed': 0, 'errors': []}
        }

        try:
            if batch_type in ['all', 'restaurants']:
                # Process restaurants
                restaurants = self.db.collection('restaurants').stream()
                for restaurant in restaurants:
                    try:
                        self.store_restaurant_embeddings(restaurant.id)
                        results['restaurants']['success'] += 1
                    except Exception as e:
                        results['restaurants']['failed'] += 1
                        results['restaurants']['errors'].append(
                            f"{restaurant.id}: {str(e)}")
                        logger.error(
                            f"Failed to process restaurant {restaurant.id}: {e}")

            if batch_type in ['all', 'clients']:
                # Process clients
                clients = self.db.collection('clients').stream()
                for client in clients:
                    try:
                        self.store_client_embeddings(client.id)
                        results['clients']['success'] += 1
                    except Exception as e:
                        results['clients']['failed'] += 1
                        results['clients']['errors'].append(
                            f"{client.id}: {str(e)}")
                        logger.error(
                            f"Failed to process client {client.id}: {e}")

            if batch_type in ['all', 'menus']:
                # Process menu items
                restaurants = self.db.collection('restaurants').stream()
                for restaurant in restaurants:
                    menu_items = restaurant.reference.collection(
                        'menu').stream()
                    for menu_item in menu_items:
                        try:
                            self.store_menu_embeddings(
                                restaurant.id, menu_item.id)
                            results['menus']['success'] += 1
                        except Exception as e:
                            results['menus']['failed'] += 1
                            results['menus']['errors'].append(
                                f"{restaurant.id}/{menu_item.id}: {str(e)}")
                            logger.error(
                                f"Failed to process menu {restaurant.id}/{menu_item.id}: {e}")

            logger.info(f"Batch embedding completed: {results}")
            return results

        except Exception as e:
            logger.error(f"Batch embedding failed: {e}")
            raise

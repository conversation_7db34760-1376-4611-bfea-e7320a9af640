// Firebase Configuration
// Bu dosyayı gerçek Firebase config bilgilerinizle güncelleyin

export const firebaseConfig = {
  apiKey: "AIzaSyCH9UECpVbYjd6YX7IsureWhQlM1g1G210",
  authDomain: "salex-2025.firebaseapp.com",
  databaseURL: "https://salex-2025-default-rtdb.firebaseio.com",
  projectId: "salex-2025",
  storageBucket: "salex-2025.firebasestorage.app",
  messagingSenderId: "512949436230",
  appId: "1:512949436230:web:ff1cd7d1f5c2b20ba1af47",
  measurementId: "G-K7RMHGY690",
};

// Test için demo kullanıcı bilgileri
export const demoCredentials = {
  email: "<EMAIL>",
  password: "demo123456",
  displayName: "<PERSON><PERSON>llanı<PERSON>",
};

// API Configuration
export const apiConfig = {
  baseURL: "http://localhost:3000",
  endpoints: {
    query: "/query",
    health: "/health",
    embedBatch: "/embed/batch",
  },
};

// Language Configuration
export const languageConfig = {
  default: "tr",
  supported: ["tr", "az", "en"],
  texts: {
    tr: {
      appName: "Qonai AI",
      tagline: "Kişiselleştirilmiş restoran ve yemek asistanınız",
      login: "Giriş Yap",
      register: "Kayıt Ol",
      email: "E-posta",
      password: "Şifre",
      name: "Ad Soyad",
      googleLogin: "Google ile Devam Et",
      demoLogin: "Demo Hesabı ile Giriş",
      logout: "Çıkış Yap",
      online: "Çevrimiçi",
      welcome:
        "Merhaba! Ben Qonai AI, kişiselleştirilmiş restoran ve yemek asistanınızım. Size nasıl yardımcı olabilirim?",
      placeholder: "Mesajınızı yazın...",
      poweredBy: "Powered by Vertex AI",
      quickActions: {
        food: "Ne yiyebilirim?",
        restaurant: "Restoran öner",
        diet: "Diyet tavsiyeleri",
        reservation: "Rezervasyon",
      },
    },
    az: {
      appName: "Qonai AI",
      tagline: "Fərdiləşdirilmiş restoran və yemək assistanınız",
      login: "Daxil ol",
      register: "Qeydiyyat",
      email: "E-poçt",
      password: "Şifrə",
      name: "Ad Soyad",
      googleLogin: "Google ilə davam et",
      demoLogin: "Demo hesabı ilə giriş",
      logout: "Çıxış",
      online: "Onlayn",
      welcome:
        "Salam! Mən Qonai AI, fərdiləşdirilmiş restoran və yemək assistanınızam. Sizə necə kömək edə bilərəm?",
      placeholder: "Mesajınızı yazın...",
      poweredBy: "Vertex AI tərəfindən dəstəklənir",
      quickActions: {
        food: "Nə yeyə bilərəm?",
        restaurant: "Restoran təklif et",
        diet: "Pəhriz məsləhətləri",
        reservation: "Rezervasiya",
      },
    },
    en: {
      appName: "Qonai AI",
      tagline: "Your personalized restaurant and food assistant",
      login: "Sign In",
      register: "Sign Up",
      email: "Email",
      password: "Password",
      name: "Full Name",
      googleLogin: "Continue with Google",
      demoLogin: "Demo Account Login",
      logout: "Sign Out",
      online: "Online",
      welcome:
        "Hello! I am Qonai AI, your personalized restaurant and food assistant. How can I help you?",
      placeholder: "Type your message...",
      poweredBy: "Powered by Vertex AI",
      quickActions: {
        food: "What can I eat?",
        restaurant: "Suggest restaurant",
        diet: "Diet advice",
        reservation: "Reservation",
      },
    },
  },
};

// Error Messages
export const errorMessages = {
  tr: {
    "auth/user-not-found": "Kullanıcı bulunamadı",
    "auth/wrong-password": "Hatalı şifre",
    "auth/email-already-in-use": "Bu e-posta adresi zaten kullanımda",
    "auth/weak-password": "Şifre çok zayıf",
    "auth/invalid-email": "Geçersiz e-posta adresi",
    "auth/popup-closed-by-user": "Giriş penceresi kapatıldı",
    "auth/cancelled-popup-request": "Giriş işlemi iptal edildi",
    "network-error": "Ağ bağlantısı hatası",
    "api-error": "Sunucu hatası",
    "unknown-error": "Bilinmeyen hata",
  },
  az: {
    "auth/user-not-found": "İstifadəçi tapılmadı",
    "auth/wrong-password": "Səhv şifrə",
    "auth/email-already-in-use": "Bu e-poçt ünvanı artıq istifadədədir",
    "auth/weak-password": "Şifrə çox zəifdir",
    "auth/invalid-email": "Yanlış e-poçt ünvanı",
    "auth/popup-closed-by-user": "Giriş pəncərəsi bağlandı",
    "auth/cancelled-popup-request": "Giriş əməliyyatı ləğv edildi",
    "network-error": "Şəbəkə bağlantısı xətası",
    "api-error": "Server xətası",
    "unknown-error": "Naməlum xəta",
  },
  en: {
    "auth/user-not-found": "User not found",
    "auth/wrong-password": "Wrong password",
    "auth/email-already-in-use": "Email already in use",
    "auth/weak-password": "Password too weak",
    "auth/invalid-email": "Invalid email address",
    "auth/popup-closed-by-user": "Login popup closed",
    "auth/cancelled-popup-request": "Login cancelled",
    "network-error": "Network connection error",
    "api-error": "Server error",
    "unknown-error": "Unknown error",
  },
};

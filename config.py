import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Google Cloud Configuration
    PROJECT_ID = "salex-2025"
    LOCATION = "us-central1"
    
    # Firebase Configuration
    SERVICE_ACCOUNT_PATH = "service-account.json"
    
    # Vertex AI Configuration
    EMBEDDING_MODEL = "text-multilingual-embedding-002"
    GENERATIVE_MODEL = "gemini-1.5-pro"
    
    # Application Configuration
    FLASK_ENV = os.getenv('FLASK_ENV', 'development')
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
    PORT = int(os.getenv('PORT', 3000))
    
    # Vector Search Configuration
    VECTOR_DIMENSION = 768  # text-multilingual-embedding-002 dimension
    MAX_SEARCH_RESULTS = 10
    
    # Supported Languages
    SUPPORTED_LANGUAGES = ['tr', 'az', 'en']
    DEFAULT_LANGUAGE = 'tr'

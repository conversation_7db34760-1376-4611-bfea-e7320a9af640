#!/usr/bin/env python3
"""
Firebase ID Token alma scripti
Bu script ile Firebase Authentication kullanarak ID token alabilirsiniz.
"""

import firebase_admin
from firebase_admin import credentials, auth
import sys

def get_custom_token_and_exchange(user_id):
    """
    Admin SDK ile custom token oluştur ve ID token'a çevir
    """
    try:
        # Firebase Admin SDK başlat
        if not firebase_admin._apps:
            cred = credentials.Certificate('service-account.json')
            firebase_admin.initialize_app(cred)
        
        # Custom token oluştur
        custom_token = auth.create_custom_token(user_id)
        print(f"Custom token oluşturuldu: {custom_token.decode()}")
        
        print("\nBu custom token'ı Firebase Auth SDK ile ID token'a çevirmeniz gerekiyor.")
        print("Web tarayıcısında şu adımları takip edin:")
        print("\n1. Firebase Console'da Authentication > Users bölümüne gidin")
        print("2. Test kullanıc<PERSON>sı oluşturun veya mevcut kullanıcıyı kullanın")
        print("3. Aşağıdaki JavaScript kodunu browser console'da çalıştırın:")
        
        print(f"""
// Firebase SDK'yı import edin (CDN veya npm)
import {{ initializeApp }} from 'firebase/app';
import {{ getAuth, signInWithCustomToken }} from 'firebase/auth';

const firebaseConfig = {{
  // Firebase config buraya
  projectId: "salex-2025"
}};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

signInWithCustomToken(auth, "{custom_token.decode()}")
  .then((userCredential) => {{
    return userCredential.user.getIdToken();
  }})
  .then((idToken) => {{
    console.log("ID Token:", idToken);
  }})
  .catch((error) => {{
    console.error("Error:", error);
  }});
""")
        
        return custom_token.decode()
        
    except Exception as e:
        print(f"Hata: {e}")
        return None

def create_test_user():
    """Test kullanıcısı oluştur"""
    try:
        if not firebase_admin._apps:
            cred = credentials.Certificate('service-account.json')
            firebase_admin.initialize_app(cred)
        
        # Test kullanıcısı oluştur
        user = auth.create_user(
            uid='test-user-123',
            email='<EMAIL>',
            password='testpassword123'
        )
        
        print(f"Test kullanıcısı oluşturuldu: {user.uid}")
        return user.uid
        
    except auth.UserAlreadyExistsError:
        print("Test kullanıcısı zaten mevcut: test-user-123")
        return 'test-user-123'
    except Exception as e:
        print(f"Kullanıcı oluşturma hatası: {e}")
        return None

def main():
    print("=== Firebase ID Token Alma Aracı ===\n")
    
    print("1. Mevcut kullanıcı UID'si ile custom token oluştur")
    print("2. Yeni test kullanıcısı oluştur")
    print("3. Çıkış")
    
    choice = input("\nSeçiminizi yapın (1-3): ").strip()
    
    if choice == '1':
        user_id = input("Kullanıcı UID'sini girin: ").strip()
        if user_id:
            get_custom_token_and_exchange(user_id)
    
    elif choice == '2':
        user_id = create_test_user()
        if user_id:
            print(f"\nTest kullanıcısı için custom token oluşturuluyor...")
            get_custom_token_and_exchange(user_id)
    
    elif choice == '3':
        print("Çıkış yapılıyor...")
        sys.exit(0)
    
    else:
        print("Geçersiz seçim!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test client for Qonai AI Backend
Bu dosya API'yi test etmek i<PERSON>in k<PERSON>.
"""

import requests
import json
import sys

# API base URL
BASE_URL = "http://localhost:3000"

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health Check: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_query(id_token, query_text, language="tr"):
    """Test query endpoint"""
    try:
        headers = {
            'Authorization': f'Bearer {id_token}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'query': query_text,
            'language': language
        }
        
        response = requests.post(f"{BASE_URL}/query", 
                               headers=headers, 
                               json=data)
        
        print(f"\nQuery: {query_text}")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {result['response']}")
        else:
            print(f"Error: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"Query test failed: {e}")
        return False

def test_embedding(id_token, endpoint_path):
    """Test embedding endpoints"""
    try:
        headers = {
            'Authorization': f'Bearer {id_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(f"{BASE_URL}{endpoint_path}", headers=headers)
        
        print(f"\nEmbedding Test: {endpoint_path}")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {result['message']}")
        else:
            print(f"Error: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"Embedding test failed: {e}")
        return False

def main():
    """Main test function"""
    print("=== Qonai AI Backend Test Client ===\n")
    
    # Test health endpoint
    print("1. Testing Health Endpoint...")
    if not test_health():
        print("❌ Health check failed. Make sure the server is running.")
        sys.exit(1)
    print("✅ Health check passed")
    
    # Get ID token from user
    print("\n2. Firebase Authentication")
    print("Bu test için Firebase ID token'ına ihtiyacınız var.")
    print("Firebase Auth SDK kullanarak token alabilir veya test için mock token kullanabilirsiniz.")
    
    id_token = input("Firebase ID Token girin (veya 'mock' yazın): ").strip()
    
    if id_token.lower() == 'mock':
        print("⚠️  Mock token kullanılıyor. Gerçek API çağrıları başarısız olabilir.")
        id_token = "mock-token-for-testing"
    
    # Test queries
    print("\n3. Testing Query Endpoint...")
    
    test_queries = [
        ("Bu gün ne yiyebilirim?", "tr"),
        ("Hangi restorana gideyim?", "tr"),
        ("McDonald's için akşam rezervasyon saatleri", "tr"),
        ("Kilo almak için ne yemeli?", "tr"),
        ("Nə yeyə bilərəm?", "az"),
        ("What can I eat today?", "en")
    ]
    
    successful_queries = 0
    for query, lang in test_queries:
        if test_query(id_token, query, lang):
            successful_queries += 1
    
    print(f"\n✅ {successful_queries}/{len(test_queries)} queries successful")
    
    # Test embedding endpoints (optional)
    print("\n4. Testing Embedding Endpoints (Optional)...")
    print("Bu testler gerçek veri gerektirir. Devam etmek istiyor musunuz? (y/n)")
    
    if input().lower() == 'y':
        embedding_tests = [
            "/embed/batch",
        ]
        
        for endpoint in embedding_tests:
            test_embedding(id_token, endpoint)
    
    print("\n=== Test Completed ===")

if __name__ == "__main__":
    main()

# Qonai AI Backend

Firebase Functions yerine Python Flask tabanlı AI asistan backend servisi. Firestore Vector Search ve Vertex AI kullanarak restoran ve yemek önerileri sunar.

## Özellikler

- 🔐 Firebase Authentication entegrasyonu
- 🔍 Firestore Vector Search ile semantik arama
- 🤖 Vertex AI ile RAG tabanlı AI yanıtları
- 🌍 Çok dilli destek (Türkçe, Azerbaycan Türkçesi, İngilizce)
- 🍽️ Kişiselleştirilmiş yemek önerileri
- 🏪 Restoran önerileri ve rezervasyon bilgileri
- 💪 Diyet hedefleri ve beslenme tavsiyeleri

## Kurulum

### 1. Gereksinimler

- Python 3.8+
- Google Cloud Project (salex-2025)
- Firebase Admin SDK service account key

### 2. Bağımlılıkları Yükle

```bash
# Virtual environment oluştur
python -m venv venv

# Virtual environment'ı aktifleştir
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Bağımlılıkları yükle
pip install -r requirements.txt
```

### 3. Konfigürasyon

`service-account.json` dosyasının proje kök dizininde olduğundan emin olun.

### 4. Firestore Vector Search İndekslerini Oluştur

```bash
# Restaurants collection için
gcloud firestore indexes composite create \
  --collection-group=restaurants \
  --query-scope=COLLECTION \
  --field-config field-path=embedding,vector-config='{"dimension":"768", "flat": "{}"}' \
  --database=(default)

# Menu items için
gcloud firestore indexes composite create \
  --collection-group=menu \
  --query-scope=COLLECTION \
  --field-config field-path=embedding,vector-config='{"dimension":"768", "flat": "{}"}' \
  --database=(default)

# Clients için
gcloud firestore indexes composite create \
  --collection-group=clients \
  --query-scope=COLLECTION \
  --field-config field-path=embedding,vector-config='{"dimension":"768", "flat": "{}"}' \
  --database=(default)
```

## Kullanım

### 1. Sunucuyu Başlat

```bash
python app.py
```

Sunucu `http://localhost:3000` adresinde çalışacak.

### 2. API Endpoints

#### Health Check
```bash
GET /health
```

#### Sorgu Gönder
```bash
POST /query
Headers: Authorization: Bearer <firebase-id-token>
Body: {
  "query": "Bu gün ne yiyebilirim?",
  "language": "tr"
}
```

#### Embeddings Oluştur

```bash
# Tek restoran için
POST /embed/restaurant/<restaurant_id>
Headers: Authorization: Bearer <firebase-id-token>

# Tek müşteri için
POST /embed/client/<client_id>
Headers: Authorization: Bearer <firebase-id-token>

# Tek menü öğesi için
POST /embed/menu/<restaurant_id>/<menu_id>
Headers: Authorization: Bearer <firebase-id-token>

# Toplu işlem
POST /embed/batch
Headers: Authorization: Bearer <firebase-id-token>
Body: {
  "type": "all"  // "restaurants", "clients", "menus", "all"
}
```

### 3. Örnek Sorgular

- "Bu gün ne yiyebilirim?"
- "Hangi restorana gideyim?"
- "McDonald's için akşam rezervasyon saatleri"
- "Kilo almak için ne yemeli?"
- "Halal yemek nerede bulabilirim?"

## Veri Yapısı

### Restaurants Collection
```json
{
  "restaurantName": "McDonald's",
  "description": "Fast food restaurant",
  "cuisine": "Fast Food",
  "city": "Baku",
  "address": "28 May Street",
  "rating": 4.2,
  "priceRange": "Budget",
  "dietaryOptions": ["Halal"],
  "workingHours": [
    {
      "day": "monday",
      "isOpen": true,
      "openTime": "08:00",
      "closeTime": "23:00"
    }
  ],
  "embedding": [0.1, 0.2, ...] // 768 boyutlu vektör
}
```

### Clients Collection
```json
{
  "name": "Ali Aliyev",
  "email": "<EMAIL>",
  "mealPreferences": {
    "dietaryRestrictions": ["Halal"],
    "allergies": ["Milk"],
    "favoriteCuisines": ["Turkish", "Italian"]
  },
  "dietaryGoals": {
    "dailyCalories": 3203,
    "goal": "Weight Gain"
  },
  "embedding": [0.1, 0.2, ...] // 768 boyutlu vektör
}
```

### Menu Subcollection
```json
{
  "name": "Big Tasty™",
  "description": "Beef burger with special sauce",
  "category": "Burgers",
  "calories": 812,
  "protein": 39,
  "carbs": 52,
  "fat": 52,
  "price": "8.50 AZN",
  "dietary": ["Halal"],
  "allergens": ["Gluten", "Sesame"],
  "embedding": [0.1, 0.2, ...] // 768 boyutlu vektör
}
```

## Dağıtım

### Google Cloud Run

```bash
# Docker image oluştur
docker build -t qonai-backend .

# Google Cloud Run'a dağıt
gcloud run deploy qonai-backend \
  --image qonai-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Heroku

```bash
# Heroku app oluştur
heroku create qonai-backend

# Deploy et
git push heroku main
```

## Geliştirme

### Test

```bash
# Unit testleri çalıştır
python -m pytest tests/

# Linting
flake8 .

# Type checking
mypy .
```

### Debugging

```bash
# Debug modunda çalıştır
export FLASK_ENV=development
export DEBUG=True
python app.py
```

## Lisans

MIT License

# Flask Configuration
FLASK_ENV=development
DEBUG=True
PORT=3000

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=salex-2025
GOOGLE_APPLICATION_CREDENTIALS=service-account.json

# Vertex AI Configuration
VERTEX_AI_LOCATION=us-central1
EMBEDDING_MODEL=text-multilingual-embedding-002
GENERATIVE_MODEL=gemini-1.5-pro

# Application Configuration
DEFAULT_LANGUAGE=tr
MAX_SEARCH_RESULTS=10
VECTOR_DIMENSION=768

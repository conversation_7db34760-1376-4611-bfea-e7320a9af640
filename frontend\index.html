<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qonai AI - Restoran ve Yemek Asistanı</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">🍽️</div>
            <h2>Qonai AI</h2>
            <div class="loading-spinner"></div>
            <p>Yükleniyor...</p>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="login-screen hidden">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">🍽️</div>
                <h1>Qonai AI</h1>
                <p>Kişiselleştirilmiş restoran ve yemek asistanınız</p>
            </div>
            
            <div class="login-form">
                <div class="form-tabs">
                    <button class="tab-btn active" data-tab="login">Giriş Yap</button>
                    <button class="tab-btn" data-tab="register">Kayıt Ol</button>
                </div>
                
                <!-- Login Tab -->
                <div id="login-tab" class="tab-content active">
                    <form id="login-form">
                        <div class="form-group">
                            <label for="login-email">E-posta</label>
                            <input type="email" id="login-email" required>
                        </div>
                        <div class="form-group">
                            <label for="login-password">Şifre</label>
                            <input type="password" id="login-password" required>
                        </div>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-sign-in-alt"></i>
                            Giriş Yap
                        </button>
                    </form>
                </div>
                
                <!-- Register Tab -->
                <div id="register-tab" class="tab-content">
                    <form id="register-form">
                        <div class="form-group">
                            <label for="register-name">Ad Soyad</label>
                            <input type="text" id="register-name" required>
                        </div>
                        <div class="form-group">
                            <label for="register-email">E-posta</label>
                            <input type="email" id="register-email" required>
                        </div>
                        <div class="form-group">
                            <label for="register-password">Şifre</label>
                            <input type="password" id="register-password" required>
                        </div>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-user-plus"></i>
                            Kayıt Ol
                        </button>
                    </form>
                </div>
                
                <div class="divider">
                    <span>veya</span>
                </div>
                
                <button id="google-login" class="btn-google">
                    <i class="fab fa-google"></i>
                    Google ile Devam Et
                </button>
            </div>
            
            <div class="demo-section">
                <p>Demo için test hesabı:</p>
                <button id="demo-login" class="btn-demo">
                    <i class="fas fa-play"></i>
                    Demo Hesabı ile Giriş
                </button>
            </div>
        </div>
    </div>

    <!-- Chat Screen -->
    <div id="chat-screen" class="chat-screen hidden">
        <!-- Header -->
        <header class="chat-header">
            <div class="header-left">
                <div class="logo">🍽️</div>
                <div class="header-info">
                    <h1>Qonai AI</h1>
                    <span class="status online">Çevrimiçi</span>
                </div>
            </div>
            <div class="header-right">
                <div class="language-selector">
                    <select id="language-select">
                        <option value="tr">🇹🇷 Türkçe</option>
                        <option value="az">🇦🇿 Azərbaycan</option>
                        <option value="en">🇺🇸 English</option>
                    </select>
                </div>
                <div class="user-menu">
                    <button class="user-avatar" id="user-menu-btn">
                        <i class="fas fa-user"></i>
                    </button>
                    <div class="user-dropdown hidden" id="user-dropdown">
                        <div class="user-info">
                            <span id="user-name">Kullanıcı</span>
                            <span id="user-email"><EMAIL></span>
                        </div>
                        <hr>
                        <button id="logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            Çıkış Yap
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Chat Container -->
        <div class="chat-container">
            <div class="chat-messages" id="chat-messages">
                <!-- Welcome Message -->
                <div class="message bot-message">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="message-text">
                            Merhaba! Ben Qonai AI, kişiselleştirilmiş restoran ve yemek asistanınızım. 
                            Size nasıl yardımcı olabilirim?
                        </div>
                        <div class="message-time">Şimdi</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions" id="quick-actions">
                <button class="quick-action" data-query="Bu gün ne yiyebilirim?">
                    <i class="fas fa-utensils"></i>
                    Ne yiyebilirim?
                </button>
                <button class="quick-action" data-query="Hangi restorana gideyim?">
                    <i class="fas fa-map-marker-alt"></i>
                    Restoran öner
                </button>
                <button class="quick-action" data-query="Kilo almak için ne yemeli?">
                    <i class="fas fa-dumbbell"></i>
                    Diyet tavsiyeleri
                </button>
                <button class="quick-action" data-query="McDonald's rezervasyon saatleri">
                    <i class="fas fa-clock"></i>
                    Rezervasyon
                </button>
            </div>

            <!-- Chat Input -->
            <div class="chat-input-container">
                <div class="chat-input">
                    <input type="text" id="message-input" placeholder="Mesajınızı yazın..." maxlength="500">
                    <button id="send-btn" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="input-footer">
                    <span class="char-count">0/500</span>
                    <span class="powered-by">Powered by Vertex AI</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Toast -->
    <div id="error-toast" class="toast error hidden">
        <i class="fas fa-exclamation-circle"></i>
        <span id="error-message">Bir hata oluştu</span>
        <button class="toast-close">&times;</button>
    </div>

    <!-- Success Toast -->
    <div id="success-toast" class="toast success hidden">
        <i class="fas fa-check-circle"></i>
        <span id="success-message">İşlem başarılı</span>
        <button class="toast-close">&times;</button>
    </div>

    <!-- Firebase SDK -->
    <script type="module" src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js"></script>
    <script type="module" src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js"></script>
    
    <!-- Main Script -->
    <script type="module" src="script.js"></script>
</body>
</html>

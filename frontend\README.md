# Qonai AI Frontend

Modern, responsive AI chatbot frontend for Qonai AI restaurant and food assistant.

## Features

- 🎨 **Modern UI/UX**: Clean, intuitive chatbot interface
- 🔐 **Firebase Authentication**: Email/password, Google login, demo account
- 🌍 **Multi-language Support**: Turkish, Azerbaijani, English
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile
- ⚡ **Real-time Chat**: Instant messaging with <PERSON> assistant
- 🚀 **Quick Actions**: Pre-defined queries for common requests
- 🎯 **Smart Features**: Character count, typing indicators, toast notifications

## Tech Stack

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with animations and gradients
- **Vanilla JavaScript**: ES6+ modules, async/await
- **Firebase SDK**: Authentication and real-time features
- **Font Awesome**: Icons
- **Google Fonts**: Inter font family

## Setup

### 1. Firebase Configuration

Update `firebase-config.js` with your Firebase project settings:

```javascript
export const firebaseConfig = {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"
};
```

### 2. Backend API

Make sure your backend is running on `http://localhost:3000`. Update `apiConfig.baseURL` in `firebase-config.js` if different.

### 3. Serve Frontend

#### Option 1: Python HTTP Server (Recommended)
```bash
cd frontend
python server.py
```
Frontend will be available at `http://localhost:8080`

#### Option 2: Node.js HTTP Server
```bash
cd frontend
npx http-server -p 8080 --cors
```

#### Option 3: Live Server (VS Code Extension)
Right-click on `index.html` and select "Open with Live Server"

## Usage

### 1. Authentication

#### Demo Account
- Click "Demo Hesabı ile Giriş" for instant access
- Credentials: `<EMAIL>` / `demo123456`

#### Email/Password
- Register new account or login with existing credentials
- Password requirements: minimum 6 characters

#### Google Login
- Click "Google ile Devam Et"
- Requires Firebase Google provider configuration

### 2. Chat Interface

#### Quick Actions
- **Ne yiyebilirim?**: Food recommendations
- **Restoran öner**: Restaurant suggestions  
- **Diyet tavsiyeleri**: Dietary advice
- **Rezervasyon**: Reservation information

#### Manual Queries
Type any food or restaurant related question in Turkish, Azerbaijani, or English.

#### Language Switching
Use the language selector in the header to switch between:
- 🇹🇷 Türkçe
- 🇦🇿 Azərbaycan  
- 🇺🇸 English

### 3. Features

#### Message Types
- **User Messages**: Right-aligned, blue gradient
- **AI Messages**: Left-aligned, white background with bot avatar

#### Interactions
- **Enter**: Send message
- **Shift+Enter**: New line
- **Character Limit**: 500 characters max
- **Auto-scroll**: Messages automatically scroll to bottom

#### User Menu
- View user name and email
- Logout functionality
- Accessible via avatar in header

## File Structure

```
frontend/
├── index.html              # Main HTML file
├── styles.css              # All CSS styles
├── script.js               # Main JavaScript logic
├── firebase-config.js      # Firebase and app configuration
├── server.py              # Simple Python HTTP server
└── README.md              # This file
```

## Customization

### Styling
Edit `styles.css` to customize:
- Colors and gradients
- Typography and spacing
- Animations and transitions
- Responsive breakpoints

### Functionality
Edit `script.js` to modify:
- Authentication flow
- Chat behavior
- API integration
- Language support

### Configuration
Edit `firebase-config.js` to update:
- Firebase settings
- API endpoints
- Language texts
- Error messages

## Browser Support

- **Chrome**: 88+
- **Firefox**: 85+
- **Safari**: 14+
- **Edge**: 88+

Requires ES6 modules support and modern JavaScript features.

## Security

- Firebase Authentication handles user security
- ID tokens are automatically managed
- CORS headers configured for API calls
- No sensitive data stored in localStorage

## Performance

- Lazy loading of Firebase SDK
- Optimized CSS animations
- Efficient DOM manipulation
- Minimal external dependencies

## Troubleshooting

### Common Issues

1. **Firebase Auth Errors**
   - Check Firebase configuration
   - Verify project settings in Firebase Console
   - Ensure authentication providers are enabled

2. **API Connection Issues**
   - Verify backend is running on correct port
   - Check CORS configuration
   - Confirm API endpoints in config

3. **Module Import Errors**
   - Serve files via HTTP server (not file://)
   - Check browser console for detailed errors
   - Verify Firebase SDK URLs

### Debug Mode

Open browser developer tools to see:
- Console logs for authentication and API calls
- Network tab for request/response details
- Application tab for Firebase auth state

## Demo Credentials

For testing purposes:
- **Email**: <EMAIL>
- **Password**: demo123456
- **Name**: Demo Kullanıcı

## License

MIT License - see main project README for details.

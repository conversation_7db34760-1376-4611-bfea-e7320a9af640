/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 2s infinite;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Login Screen */
.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.login-container {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.login-header h1 {
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
}

/* Form Tabs */
.form-tabs {
    display: flex;
    margin-bottom: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 4px;
}

.tab-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.btn-primary {
    width: 100%;
    padding: 0.75rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
    margin-bottom: 1rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-google {
    width: 100%;
    padding: 0.75rem;
    background: white;
    color: #333;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.btn-google:hover {
    border-color: #667eea;
    color: #667eea;
}

.btn-demo {
    width: 100%;
    padding: 0.5rem;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-demo:hover {
    background: #e9ecef;
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: #666;
    font-size: 0.9rem;
}

.demo-section {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.demo-section p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

/* Chat Screen */
.chat-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
}

/* Chat Header */
.chat-header {
    background: white;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-info h1 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #333;
}

.status {
    font-size: 0.8rem;
    color: #28a745;
    font-weight: 500;
}

.status.online::before {
    content: '●';
    margin-right: 0.25rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.language-selector select {
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: white;
    cursor: pointer;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    min-width: 200px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.user-info {
    margin-bottom: 0.5rem;
}

.user-info span {
    display: block;
    font-size: 0.9rem;
}

.user-info span:first-child {
    font-weight: 600;
    color: #333;
}

.user-info span:last-child {
    color: #666;
}

#logout-btn {
    width: 100%;
    padding: 0.5rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    scroll-behavior: smooth;
}

/* Messages */
.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: #28a745;
    color: white;
    margin-right: 0;
    margin-left: 1rem;
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.user-message .message-content {
    text-align: right;
}

.message-text {
    background: white;
    padding: 1rem;
    border-radius: 18px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    line-height: 1.5;
}

.user-message .message-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.message-time {
    font-size: 0.75rem;
    color: #666;
    margin-top: 0.5rem;
    padding: 0 0.5rem;
}

.user-message .message-time {
    text-align: right;
}

/* Quick Actions */
.quick-actions {
    padding: 1rem 1.5rem;
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.quick-actions::-webkit-scrollbar {
    display: none;
}

.quick-action {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-action:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Chat Input */
.chat-input-container {
    padding: 1rem 1.5rem;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chat-input {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 0.5rem;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.chat-input:focus-within {
    border-color: #667eea;
}

#message-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    outline: none;
}

#send-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: #667eea;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

#send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

#send-btn:not(:disabled):hover {
    background: #5a6fd8;
    transform: scale(1.05);
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #666;
}

/* Toast Messages */
.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 10000;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast.error {
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.toast.success {
    border-left: 4px solid #28a745;
    color: #28a745;
}

.toast-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
    margin-left: 1rem;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .chat-header {
        padding: 1rem;
    }
    
    .header-right {
        gap: 0.5rem;
    }
    
    .language-selector select {
        font-size: 0.8rem;
        padding: 0.25rem;
    }
    
    .chat-messages {
        padding: 1rem;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .quick-actions {
        padding: 0.5rem 1rem;
    }
    
    .chat-input-container {
        padding: 1rem;
    }
}

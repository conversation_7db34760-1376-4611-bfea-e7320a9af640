from flask import Flask, request, jsonify
from flask_cors import CORS
from firebase_admin import credentials, initialize_app, auth, firestore
from config import Config
from embeddings import EmbeddingService
from query import QueryProcessor
import logging
import traceback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Firebase Admin SDK
try:
    cred = credentials.Certificate(Config.SERVICE_ACCOUNT_PATH)
    initialize_app(cred, {
        'projectId': Config.PROJECT_ID
    })
    db = firestore.client()
    logger.info("Firebase Admin SDK initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Firebase: {e}")
    raise

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize services
embedding_service = EmbeddingService()
query_processor = QueryProcessor(db, embedding_service)


def authenticate_request():
    """Middleware to authenticate Firebase ID tokens"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None, jsonify({'error': 'Unauthorized: Token required'}), 401

    id_token = auth_header.split('Bearer ')[1]
    try:
        decoded_token = auth.verify_id_token(id_token)
        return decoded_token, None, None
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        return None, jsonify({'error': 'Invalid token'}), 401


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'qonai-ai-backend'})


@app.route('/query', methods=['POST'])
def handle_query():
    """Main query processing endpoint"""
    user, error, status = authenticate_request()
    if error:
        return error, status

    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({'error': 'Query text is required'}), 400

        query_text = data.get('query')
        language = data.get('language', Config.DEFAULT_LANGUAGE)

        if language not in Config.SUPPORTED_LANGUAGES:
            language = Config.DEFAULT_LANGUAGE

        logger.info(f"Processing query for user {user['uid']}: {query_text}")

        response = query_processor.process_query(
            query_text, user['uid'], language)

        return jsonify({
            'response': response,
            'language': language,
            'user_id': user['uid']
        })

    except Exception as e:
        logger.error(f"Query processing error: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': 'Internal server error'}), 500


@app.route('/embed/restaurant/<restaurant_id>', methods=['POST'])
def embed_restaurant(restaurant_id):
    """Create embeddings for a restaurant"""
    user, error, status = authenticate_request()
    if error:
        return error, status

    try:
        embedding_service.store_restaurant_embeddings(restaurant_id)
        logger.info(f"Restaurant embeddings created for {restaurant_id}")
        return jsonify({'message': 'Restaurant embeddings created successfully'})
    except Exception as e:
        logger.error(f"Restaurant embedding error: {e}")
        return jsonify({'error': 'Failed to create restaurant embeddings'}), 500


@app.route('/embed/client/<client_id>', methods=['POST'])
def embed_client(client_id):
    """Create embeddings for a client"""
    user, error, status = authenticate_request()
    if error:
        return error, status

    try:
        embedding_service.store_client_embeddings(client_id)
        logger.info(f"Client embeddings created for {client_id}")
        return jsonify({'message': 'Client embeddings created successfully'})
    except Exception as e:
        logger.error(f"Client embedding error: {e}")
        return jsonify({'error': 'Failed to create client embeddings'}), 500


@app.route('/embed/menu/<restaurant_id>/<menu_id>', methods=['POST'])
def embed_menu(restaurant_id, menu_id):
    """Create embeddings for a menu item"""
    user, error, status = authenticate_request()
    if error:
        return error, status

    try:
        embedding_service.store_menu_embeddings(restaurant_id, menu_id)
        logger.info(f"Menu embeddings created for {restaurant_id}/{menu_id}")
        return jsonify({'message': 'Menu embeddings created successfully'})
    except Exception as e:
        logger.error(f"Menu embedding error: {e}")
        return jsonify({'error': 'Failed to create menu embeddings'}), 500


@app.route('/embed/batch', methods=['POST'])
def embed_batch():
    """Batch create embeddings for all data"""
    user, error, status = authenticate_request()
    if error:
        return error, status

    try:
        data = request.get_json()
        # 'restaurants', 'clients', 'menus', 'all'
        batch_type = data.get('type', 'all')

        results = embedding_service.batch_create_embeddings(batch_type)

        return jsonify({
            'message': 'Batch embeddings created successfully',
            'results': results
        })
    except Exception as e:
        logger.error(f"Batch embedding error: {e}")
        return jsonify({'error': 'Failed to create batch embeddings'}), 500


@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500


if __name__ == '__main__':
    app.run(
        host='0.0.0.0',
        port=Config.PORT,
        debug=Config.DEBUG
    )

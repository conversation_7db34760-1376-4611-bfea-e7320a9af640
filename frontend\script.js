// Firebase Configuration
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import {
  getAuth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";

// Firebase Config - Using real config for salex-2025
const firebaseConfig = {
  apiKey: "AIzaSyBvOkBwN0kwOcJzd4zdp73cgcT-SjdU-cw",
  authDomain: "salex-2025.firebaseapp.com",
  projectId: "salex-2025",
  storageBucket: "salex-2025.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdef123456789012345678",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

// API Configuration
const API_BASE_URL = "http://localhost:3000";

// Global State
let currentUser = null;
let currentLanguage = "tr";

// DOM Elements
const loadingScreen = document.getElementById("loading-screen");
const loginScreen = document.getElementById("login-screen");
const chatScreen = document.getElementById("chat-screen");
const loginForm = document.getElementById("login-form");
const registerForm = document.getElementById("register-form");
const googleLoginBtn = document.getElementById("google-login");
const demoLoginBtn = document.getElementById("demo-login");
const chatMessages = document.getElementById("chat-messages");
const messageInput = document.getElementById("message-input");
const sendBtn = document.getElementById("send-btn");
const quickActions = document.getElementById("quick-actions");
const languageSelect = document.getElementById("language-select");
const userMenuBtn = document.getElementById("user-menu-btn");
const userDropdown = document.getElementById("user-dropdown");
const logoutBtn = document.getElementById("logout-btn");
const userNameSpan = document.getElementById("user-name");
const userEmailSpan = document.getElementById("user-email");

// Initialize App
document.addEventListener("DOMContentLoaded", () => {
  initializeApp();
});

function initializeApp() {
  // Show loading screen
  showScreen("loading");

  // Setup event listeners
  setupEventListeners();

  // Check authentication state
  onAuthStateChanged(auth, (user) => {
    setTimeout(() => {
      if (user) {
        currentUser = user;
        showChatScreen();
      } else {
        showLoginScreen();
      }
    }, 1500); // Show loading for at least 1.5 seconds
  });
}

function setupEventListeners() {
  // Tab switching
  document.querySelectorAll(".tab-btn").forEach((btn) => {
    btn.addEventListener("click", () => switchTab(btn.dataset.tab));
  });

  // Form submissions
  loginForm.addEventListener("submit", handleLogin);
  registerForm.addEventListener("submit", handleRegister);

  // Social login
  googleLoginBtn.addEventListener("click", handleGoogleLogin);
  demoLoginBtn.addEventListener("click", handleDemoLogin);

  // Chat functionality
  messageInput.addEventListener("input", handleInputChange);
  messageInput.addEventListener("keypress", handleKeyPress);
  sendBtn.addEventListener("click", sendMessage);

  // Quick actions
  quickActions.addEventListener("click", handleQuickAction);

  // Language change
  languageSelect.addEventListener("change", handleLanguageChange);

  // User menu
  userMenuBtn.addEventListener("click", toggleUserMenu);
  logoutBtn.addEventListener("click", handleLogout);

  // Close user menu when clicking outside
  document.addEventListener("click", (e) => {
    if (!userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
      userDropdown.classList.add("hidden");
    }
  });

  // Toast close buttons
  document.querySelectorAll(".toast-close").forEach((btn) => {
    btn.addEventListener("click", () => {
      btn.parentElement.classList.add("hidden");
    });
  });
}

// Screen Management
function showScreen(screen) {
  loadingScreen.classList.add("hidden");
  loginScreen.classList.add("hidden");
  chatScreen.classList.add("hidden");

  switch (screen) {
    case "loading":
      loadingScreen.classList.remove("hidden");
      break;
    case "login":
      loginScreen.classList.remove("hidden");
      break;
    case "chat":
      chatScreen.classList.remove("hidden");
      break;
  }
}

function showLoginScreen() {
  showScreen("login");
}

function showChatScreen() {
  showScreen("chat");
  updateUserInfo();
  addWelcomeMessage();
}

// Authentication Functions
async function handleLogin(e) {
  e.preventDefault();

  const email = document.getElementById("login-email").value;
  const password = document.getElementById("login-password").value;

  try {
    showLoading(true);
    await signInWithEmailAndPassword(auth, email, password);
    showToast("Giriş başarılı!", "success");
  } catch (error) {
    console.error("Login error:", error);
    showToast(getErrorMessage(error.code), "error");
  } finally {
    showLoading(false);
  }
}

async function handleRegister(e) {
  e.preventDefault();

  const name = document.getElementById("register-name").value;
  const email = document.getElementById("register-email").value;
  const password = document.getElementById("register-password").value;

  try {
    showLoading(true);
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      email,
      password
    );

    // Update profile with name
    await userCredential.user.updateProfile({
      displayName: name,
    });

    showToast("Kayıt başarılı!", "success");
  } catch (error) {
    console.error("Register error:", error);
    showToast(getErrorMessage(error.code), "error");
  } finally {
    showLoading(false);
  }
}

async function handleGoogleLogin() {
  try {
    showLoading(true);
    await signInWithPopup(auth, googleProvider);
    showToast("Google ile giriş başarılı!", "success");
  } catch (error) {
    console.error("Google login error:", error);
    showToast(getErrorMessage(error.code), "error");
  } finally {
    showLoading(false);
  }
}

async function handleDemoLogin() {
  // Demo credentials
  const demoEmail = "<EMAIL>";
  const demoPassword = "demo123456";

  try {
    showLoading(true);
    await signInWithEmailAndPassword(auth, demoEmail, demoPassword);
    showToast("Demo hesabı ile giriş yapıldı!", "success");
  } catch (error) {
    // If demo user doesn't exist, create it
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        demoEmail,
        demoPassword
      );
      await userCredential.user.updateProfile({
        displayName: "Demo Kullanıcı",
      });
      showToast("Demo hesabı oluşturuldu ve giriş yapıldı!", "success");
    } catch (createError) {
      console.error("Demo login error:", createError);
      showToast("Demo hesabı oluşturulamadı", "error");
    }
  } finally {
    showLoading(false);
  }
}

async function handleLogout() {
  try {
    await signOut(auth);
    showToast("Çıkış yapıldı", "success");
  } catch (error) {
    console.error("Logout error:", error);
    showToast("Çıkış yapılırken hata oluştu", "error");
  }
}

// UI Helper Functions
function switchTab(tabName) {
  // Update tab buttons
  document.querySelectorAll(".tab-btn").forEach((btn) => {
    btn.classList.remove("active");
  });
  document.querySelector(`[data-tab="${tabName}"]`).classList.add("active");

  // Update tab content
  document.querySelectorAll(".tab-content").forEach((content) => {
    content.classList.remove("active");
  });
  document.getElementById(`${tabName}-tab`).classList.add("active");
}

function updateUserInfo() {
  if (currentUser) {
    userNameSpan.textContent = currentUser.displayName || "Kullanıcı";
    userEmailSpan.textContent = currentUser.email;
  }
}

function toggleUserMenu() {
  userDropdown.classList.toggle("hidden");
}

function handleInputChange() {
  const value = messageInput.value.trim();
  sendBtn.disabled = !value;

  // Update character count
  const charCount = document.querySelector(".char-count");
  charCount.textContent = `${messageInput.value.length}/500`;
}

function handleKeyPress(e) {
  if (e.key === "Enter" && !e.shiftKey) {
    e.preventDefault();
    sendMessage();
  }
}

function handleQuickAction(e) {
  if (e.target.classList.contains("quick-action")) {
    const query = e.target.dataset.query;
    messageInput.value = query;
    sendMessage();
  }
}

function handleLanguageChange() {
  currentLanguage = languageSelect.value;
  updateLanguageTexts();
}

// Chat Functions
async function sendMessage() {
  const message = messageInput.value.trim();
  if (!message || !currentUser) return;

  // Add user message to chat
  addMessage(message, "user");

  // Clear input
  messageInput.value = "";
  handleInputChange();

  // Hide quick actions after first message
  if (quickActions.children.length > 0) {
    quickActions.style.display = "none";
  }

  try {
    // Get ID token
    const idToken = await currentUser.getIdToken();

    // Send to API
    const response = await fetch(`${API_BASE_URL}/query`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
        query: message,
        language: currentLanguage,
      }),
    });

    if (response.ok) {
      const data = await response.json();
      addMessage(data.response, "bot");
    } else {
      const errorData = await response.json();
      addMessage(`Üzgünüm, bir hata oluştu: ${errorData.error}`, "bot");
    }
  } catch (error) {
    console.error("Send message error:", error);
    addMessage(
      "Üzgünüm, şu anda mesajınızı işleyemiyorum. Lütfen daha sonra tekrar deneyin.",
      "bot"
    );
  }
}

function addMessage(text, sender) {
  const messageDiv = document.createElement("div");
  messageDiv.className = `message ${sender}-message`;

  const avatar = document.createElement("div");
  avatar.className = "message-avatar";
  avatar.textContent = sender === "bot" ? "🤖" : "👤";

  const content = document.createElement("div");
  content.className = "message-content";

  const messageText = document.createElement("div");
  messageText.className = "message-text";
  messageText.textContent = text;

  const messageTime = document.createElement("div");
  messageTime.className = "message-time";
  messageTime.textContent = new Date().toLocaleTimeString("tr-TR", {
    hour: "2-digit",
    minute: "2-digit",
  });

  content.appendChild(messageText);
  content.appendChild(messageTime);

  messageDiv.appendChild(avatar);
  messageDiv.appendChild(content);

  chatMessages.appendChild(messageDiv);
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addWelcomeMessage() {
  // Clear existing messages except welcome
  const existingMessages = chatMessages.querySelectorAll(".message");
  if (existingMessages.length <= 1) {
    return; // Welcome message already exists
  }
}

// Utility Functions
function showLoading(show) {
  const buttons = document.querySelectorAll('button[type="submit"]');
  buttons.forEach((btn) => {
    btn.disabled = show;
    if (show) {
      btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Yükleniyor...';
    } else {
      // Reset button text based on context
      if (btn.closest("#login-tab")) {
        btn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Giriş Yap';
      } else if (btn.closest("#register-tab")) {
        btn.innerHTML = '<i class="fas fa-user-plus"></i> Kayıt Ol';
      }
    }
  });
}

function showToast(message, type) {
  const toast = document.getElementById(`${type}-toast`);
  const messageSpan = document.getElementById(`${type}-message`);

  messageSpan.textContent = message;
  toast.classList.remove("hidden");

  // Auto hide after 5 seconds
  setTimeout(() => {
    toast.classList.add("hidden");
  }, 5000);
}

function getErrorMessage(errorCode) {
  const errorMessages = {
    "auth/user-not-found": "Kullanıcı bulunamadı",
    "auth/wrong-password": "Hatalı şifre",
    "auth/email-already-in-use": "Bu e-posta adresi zaten kullanımda",
    "auth/weak-password": "Şifre çok zayıf",
    "auth/invalid-email": "Geçersiz e-posta adresi",
    "auth/popup-closed-by-user": "Giriş penceresi kapatıldı",
    "auth/cancelled-popup-request": "Giriş işlemi iptal edildi",
  };

  return errorMessages[errorCode] || "Bir hata oluştu";
}

function updateLanguageTexts() {
  const texts = {
    tr: {
      welcome:
        "Merhaba! Ben Qonai AI, kişiselleştirilmiş restoran ve yemek asistanınızım. Size nasıl yardımcı olabilirim?",
      placeholder: "Mesajınızı yazın...",
      poweredBy: "Powered by Vertex AI",
    },
    az: {
      welcome:
        "Salam! Mən Qonai AI, fərdiləşdirilmiş restoran və yemək assistanınızam. Sizə necə kömək edə bilərəm?",
      placeholder: "Mesajınızı yazın...",
      poweredBy: "Vertex AI tərəfindən dəstəklənir",
    },
    en: {
      welcome:
        "Hello! I am Qonai AI, your personalized restaurant and food assistant. How can I help you?",
      placeholder: "Type your message...",
      poweredBy: "Powered by Vertex AI",
    },
  };

  const currentTexts = texts[currentLanguage];

  // Update placeholder
  messageInput.placeholder = currentTexts.placeholder;

  // Update powered by text
  document.querySelector(".powered-by").textContent = currentTexts.poweredBy;

  // Update welcome message if it's the only message
  const messages = chatMessages.querySelectorAll(".message");
  if (messages.length === 1) {
    const welcomeText = messages[0].querySelector(".message-text");
    welcomeText.textContent = currentTexts.welcome;
  }
}

from google.cloud import aiplatform
from firebase_admin import firestore
from config import Config
from embeddings import EmbeddingService
import json
import logging
import re
from datetime import datetime
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)


class QueryProcessor:
    def __init__(self, db: firestore.Client, embedding_service: EmbeddingService):
        """Initialize the query processor"""
        self.db = db
        self.embedding_service = embedding_service

        try:
            aiplatform.init(project=Config.PROJECT_ID,
                            location=Config.LOCATION)
            logger.info("QueryProcessor initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize QueryProcessor: {e}")
            raise

    def process_query(self, query_text: str, user_id: str, language: str = 'tr') -> str:
        """Process user query and return AI-generated response"""
        try:
            # Get user data
            user_data = self._get_user_data(user_id)

            # Generate query embedding
            query_vector = self.embedding_service.generate_embedding(
                query_text)

            # Determine query type and process accordingly
            query_type = self._classify_query(query_text, language)

            if query_type == 'food_recommendation':
                return self._handle_food_recommendation(query_text, query_vector, user_data, language)
            elif query_type == 'restaurant_recommendation':
                return self._handle_restaurant_recommendation(query_text, query_vector, user_data, language)
            elif query_type == 'reservation_hours':
                return self._handle_reservation_hours(query_text, query_vector, user_data, language)
            elif query_type == 'dietary_advice':
                return self._handle_dietary_advice(query_text, query_vector, user_data, language)
            else:
                return self._handle_general_query(query_text, query_vector, user_data, language)

        except Exception as e:
            logger.error(f"Query processing failed: {e}")
            return self._get_error_message(language)

    def _get_user_data(self, user_id: str) -> Dict[str, Any]:
        """Get user data from Firestore"""
        try:
            user_doc = self.db.collection('clients').document(user_id).get()
            if not user_doc.exists:
                raise ValueError(f'User {user_id} not found')
            return user_doc.to_dict()
        except Exception as e:
            logger.error(f"Failed to get user data for {user_id}: {e}")
            raise

    def _classify_query(self, query_text: str, language: str) -> str:
        """Classify the type of query"""
        query_lower = query_text.lower()

        # Food recommendation patterns
        food_patterns = {
            'tr': ['ne yiyebilirim', 'ne yemeli', 'yemek öner', 'ne yesem'],
            'az': ['nə yeyə bilərəm', 'nə yeməli', 'yemək təklif et'],
            'en': ['what can i eat', 'what should i eat', 'food recommendation']
        }

        # Restaurant recommendation patterns
        restaurant_patterns = {
            'tr': ['hangi restorana', 'restoran öner', 'nerede yesem', 'en iyi restoran'],
            'az': ['hansı restorana', 'restoran təklif et', 'harada yeyim'],
            'en': ['which restaurant', 'restaurant recommendation', 'where to eat']
        }

        # Reservation patterns
        reservation_patterns = {
            'tr': ['rezervasyon', 'açık mı', 'saat kaçta', 'çalışma saatleri'],
            'az': ['rezervasiya', 'açıqdır', 'saat neçədə', 'iş saatları'],
            'en': ['reservation', 'opening hours', 'what time', 'working hours']
        }

        # Dietary advice patterns
        dietary_patterns = {
            'tr': ['kilo', 'diyet', 'sağlıklı', 'kalori', 'beslenme'],
            'az': ['çəki', 'pəhriz', 'sağlam', 'kalori', 'qidalanma'],
            'en': ['weight', 'diet', 'healthy', 'calorie', 'nutrition']
        }

        # Check patterns
        for pattern in food_patterns.get(language, food_patterns['tr']):
            if pattern in query_lower:
                return 'food_recommendation'

        for pattern in restaurant_patterns.get(language, restaurant_patterns['tr']):
            if pattern in query_lower:
                return 'restaurant_recommendation'

        for pattern in reservation_patterns.get(language, reservation_patterns['tr']):
            if pattern in query_lower:
                return 'reservation_hours'

        for pattern in dietary_patterns.get(language, dietary_patterns['tr']):
            if pattern in query_lower:
                return 'dietary_advice'

        return 'general'

    def _vector_search(self, collection: str, field: str, query_vector: List[float],
                       limit: int = 5, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Perform vector search in Firestore"""
        try:
            query = self.db.collection(collection)

            # Apply filters if provided
            if filters:
                for key, value in filters.items():
                    query = query.where(key, '==', value)

            # Perform vector search
            vector_query = query.find_nearest(
                vector_field=field,
                query_vector=firestore.VectorValue(query_vector),
                limit=limit,
                distance_measure=firestore.DistanceMeasure.COSINE
            )

            results = []
            for doc in vector_query.stream():
                results.append({
                    'id': doc.id,
                    'data': doc.to_dict(),
                    'distance': getattr(doc, '_distance', 0)
                })

            return results

        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return []

    def _menu_search(self, restaurant_id: str, query_vector: List[float],
                     limit: int = 5) -> List[Dict[str, Any]]:
        """Search menu items within a restaurant"""
        try:
            menu_ref = self.db.collection('restaurants').document(
                restaurant_id).collection('menu')

            vector_query = menu_ref.find_nearest(
                vector_field='embedding',
                query_vector=firestore.VectorValue(query_vector),
                limit=limit,
                distance_measure=firestore.DistanceMeasure.COSINE
            )

            results = []
            for doc in vector_query.stream():
                results.append({
                    'id': doc.id,
                    'data': doc.to_dict(),
                    'distance': getattr(doc, '_distance', 0)
                })

            return results

        except Exception as e:
            logger.error(
                f"Menu search failed for restaurant {restaurant_id}: {e}")
            return []

    def _handle_food_recommendation(self, query_text: str, query_vector: List[float],
                                    user_data: Dict[str, Any], language: str) -> str:
        """Handle food recommendation queries"""
        try:
            # Get user preferences
            dietary_restrictions = user_data.get(
                'mealPreferences', {}).get('dietaryRestrictions', [])
            allergies = user_data.get(
                'mealPreferences', {}).get('allergies', [])
            dietary_goals = user_data.get('dietaryGoals', {})

            # Search for relevant restaurants
            restaurants = self._vector_search(
                'restaurants', 'embedding', query_vector, 3, {'city': 'Baku'})

            # Collect menu items from relevant restaurants
            all_dishes = []
            for restaurant in restaurants:
                menu_items = self._menu_search(
                    restaurant['id'], query_vector, 5)

                # Filter based on dietary restrictions and allergies
                filtered_items = []
                for item in menu_items:
                    item_data = item['data']

                    # Check allergies
                    item_allergens = item_data.get('allergens', [])
                    if any(allergen in item_allergens for allergen in allergies):
                        continue

                    # Check dietary restrictions
                    item_dietary = item_data.get('dietary', [])
                    if dietary_restrictions and not any(restriction in item_dietary for restriction in dietary_restrictions):
                        continue

                    # Add restaurant info to item
                    item['restaurant'] = restaurant['data']
                    filtered_items.append(item)

                all_dishes.extend(filtered_items)

            # Generate AI response
            return self._generate_ai_response(query_text, user_data, all_dishes, 'food', language)

        except Exception as e:
            logger.error(f"Food recommendation failed: {e}")
            return self._get_error_message(language)

    def _handle_restaurant_recommendation(self, query_text: str, query_vector: List[float],
                                          user_data: Dict[str, Any], language: str) -> str:
        """Handle restaurant recommendation queries"""
        try:
            # Search for relevant restaurants
            restaurants = self._vector_search(
                'restaurants', 'embedding', query_vector, 5, {'city': 'Baku'})

            # Generate AI response
            return self._generate_ai_response(query_text, user_data, restaurants, 'restaurant', language)

        except Exception as e:
            logger.error(f"Restaurant recommendation failed: {e}")
            return self._get_error_message(language)

    def _handle_reservation_hours(self, query_text: str, query_vector: List[float],
                                  user_data: Dict[str, Any], language: str) -> str:
        """Handle reservation hours queries"""
        try:
            # Extract restaurant name from query
            restaurant_name = self._extract_restaurant_name(
                query_text, language)

            if restaurant_name:
                # Search for specific restaurant
                restaurants = self.db.collection('restaurants')\
                    .where('restaurantName', '>=', restaurant_name)\
                    .where('restaurantName', '<=', restaurant_name + '\uf8ff')\
                    .limit(1).stream()

                restaurant_list = [{'id': doc.id, 'data': doc.to_dict()}
                                   for doc in restaurants]
            else:
                # Use vector search if no specific restaurant mentioned
                restaurant_list = self._vector_search(
                    'restaurants', 'embedding', query_vector, 1, {'city': 'Baku'})

            if not restaurant_list:
                return self._get_no_restaurant_message(language)

            restaurant = restaurant_list[0]['data']
            today = datetime.now().strftime('%A').lower()

            # Find working hours for today
            working_hours = None
            for hours in restaurant.get('workingHours', []):
                if hours.get('day', '').lower() == today and hours.get('isOpen', False):
                    working_hours = hours
                    break

            if working_hours:
                return self._format_hours_response(restaurant['restaurantName'], today,
                                                   working_hours['openTime'], working_hours['closeTime'], language)
            else:
                return self._format_closed_response(restaurant['restaurantName'], today, language)

        except Exception as e:
            logger.error(f"Reservation hours query failed: {e}")
            return self._get_error_message(language)

    def _handle_dietary_advice(self, query_text: str, query_vector: List[float],
                               user_data: Dict[str, Any], language: str) -> str:
        """Handle dietary advice queries"""
        try:
            # Get user dietary goals
            dietary_goals = user_data.get('dietaryGoals', {})

            # Search for relevant restaurants and menu items
            restaurants = self._vector_search(
                'restaurants', 'embedding', query_vector, 3, {'city': 'Baku'})

            # Collect menu items with nutritional info
            nutritional_items = []
            for restaurant in restaurants:
                menu_items = self._menu_search(
                    restaurant['id'], query_vector, 5)
                for item in menu_items:
                    if 'calories' in item['data']:
                        item['restaurant'] = restaurant['data']
                        nutritional_items.append(item)

            # Generate AI response with dietary advice
            return self._generate_ai_response(query_text, user_data, nutritional_items, 'dietary', language)

        except Exception as e:
            logger.error(f"Dietary advice query failed: {e}")
            return self._get_error_message(language)

    def _handle_general_query(self, query_text: str, query_vector: List[float],
                              user_data: Dict[str, Any], language: str) -> str:
        """Handle general queries"""
        try:
            # Search for relevant restaurants
            restaurants = self._vector_search(
                'restaurants', 'embedding', query_vector, 3, {'city': 'Baku'})

            # Generate AI response
            return self._generate_ai_response(query_text, user_data, restaurants, 'general', language)

        except Exception as e:
            logger.error(f"General query failed: {e}")
            return self._get_error_message(language)

    def _generate_ai_response(self, query_text: str, user_data: Dict[str, Any],
                              context_data: List[Dict[str, Any]], response_type: str, language: str) -> str:
        """Generate AI response using Vertex AI"""
        try:
            # Create context string
            context = self._format_context(context_data, response_type)

            # Create prompt based on language and response type
            prompt = self._create_prompt(
                query_text, user_data, context, response_type, language)

            # Generate response using Gemini
            model = aiplatform.GenerativeModel(Config.GENERATIVE_MODEL)
            response = model.generate_content(prompt)

            return response.text

        except Exception as e:
            logger.error(f"AI response generation failed: {e}")
            return self._get_error_message(language)

    def _format_context(self, context_data: List[Dict[str, Any]], response_type: str) -> str:
        """Format context data for AI prompt"""
        if not context_data:
            return "No relevant data found."

        context_parts = []

        if response_type == 'food':
            for item in context_data[:5]:  # Limit to top 5 items
                data = item['data']
                restaurant = item.get('restaurant', {})
                context_parts.append(
                    f"Yemek: {data.get('name', 'N/A')} - "
                    f"Restoran: {restaurant.get('restaurantName', 'N/A')} - "
                    f"Açıklama: {data.get('description', 'N/A')} - "
                    f"Kalori: {data.get('calories', 'N/A')} - "
                    f"Fiyat: {data.get('price', 'N/A')}"
                )

        elif response_type == 'restaurant':
            for item in context_data[:5]:
                data = item['data']
                context_parts.append(
                    f"Restoran: {data.get('restaurantName', 'N/A')} - "
                    f"Açıklama: {data.get('description', 'N/A')} - "
                    f"Mutfak: {data.get('cuisine', 'N/A')} - "
                    f"Puan: {data.get('rating', 'N/A')} - "
                    f"Adres: {data.get('address', 'N/A')}"
                )

        elif response_type == 'dietary':
            for item in context_data[:5]:
                data = item['data']
                restaurant = item.get('restaurant', {})
                context_parts.append(
                    f"Yemek: {data.get('name', 'N/A')} - "
                    f"Restoran: {restaurant.get('restaurantName', 'N/A')} - "
                    f"Kalori: {data.get('calories', 'N/A')} - "
                    f"Protein: {data.get('protein', 'N/A')}g - "
                    f"Karbonhidrat: {data.get('carbs', 'N/A')}g - "
                    f"Yağ: {data.get('fat', 'N/A')}g"
                )

        else:  # general
            for item in context_data[:3]:
                data = item['data']
                context_parts.append(
                    f"Restoran: {data.get('restaurantName', 'N/A')} - "
                    f"Açıklama: {data.get('description', 'N/A')}"
                )

        return '\n'.join(context_parts)

    def _create_prompt(self, query_text: str, user_data: Dict[str, Any], context: str,
                       response_type: str, language: str) -> str:
        """Create AI prompt based on query type and language"""

        # User preferences summary
        preferences = user_data.get('mealPreferences', {})
        dietary_goals = user_data.get('dietaryGoals', {})

        user_info = f"""
Kullanıcı Bilgileri:
- Diyet Kısıtlamaları: {', '.join(preferences.get('dietaryRestrictions', []))}
- Alerjiler: {', '.join(preferences.get('allergies', []))}
- Favori Mutfaklar: {', '.join(preferences.get('favoriteCuisines', []))}
- Diyet Hedefleri: {json.dumps(dietary_goals, ensure_ascii=False)}
"""

        if language == 'tr':
            base_prompt = f"""
Sen Qonai AI asistanısın. Kullanıcıların yemek ve restoran sorularına yardımcı oluyorsun.

Kullanıcı Sorusu: {query_text}

{user_info}

Bağlam Bilgileri:
{context}

Lütfen kullanıcının sorusuna Türkçe olarak, kişiselleştirilmiş ve yararlı bir yanıt ver.
Kullanıcının diyet kısıtlamalarını ve alerjilerini dikkate al.
Yanıtın samimi ve yardımcı olsun.
"""
        elif language == 'az':
            base_prompt = f"""
Sən Qonai AI assistanısan. İstifadəçilərin yemək və restoran suallarına kömək edirsən.

İstifadəçi Sualı: {query_text}

{user_info}

Kontekst Məlumatları:
{context}

Zəhmət olmasa istifadəçinin sualına Azərbaycan dilində, fərdiləşdirilmiş və faydalı cavab ver.
İstifadəçinin pəhriz məhdudiyyətlərini və allergiyalarını nəzərə al.
Cavabın səmimi və köməkçi olsun.
"""
        else:  # English
            base_prompt = f"""
You are Qonai AI assistant. You help users with food and restaurant questions.

User Question: {query_text}

{user_info}

Context Information:
{context}

Please provide a personalized and helpful response in English to the user's question.
Consider the user's dietary restrictions and allergies.
Make your response friendly and helpful.
"""

        return base_prompt

    def _extract_restaurant_name(self, query_text: str, language: str) -> Optional[str]:
        """Extract restaurant name from query"""
        import re

        patterns = {
            'tr': [r'(.+?)\s+restoranı', r'(.+?)\s+için', r'(.+?)\s+da\b', r'(.+?)\s+de\b'],
            'az': [r'(.+?)\s+restoranı', r'(.+?)\s+üçün', r'(.+?)\s+da\b'],
            'en': [r'(.+?)\s+restaurant', r'(.+?)\s+for\b', r'at\s+(.+?)']
        }

        for pattern in patterns.get(language, patterns['tr']):
            match = re.search(pattern, query_text, re.IGNORECASE)
            if match:
                return match.group(1).strip()

        return None

    def _get_error_message(self, language: str) -> str:
        """Get error message in specified language"""
        messages = {
            'tr': 'Üzgünüm, şu anda sorgunuzu işleyemiyorum. Lütfen daha sonra tekrar deneyin.',
            'az': 'Təəssüf ki, hazırda sorğunuzu işləyə bilmirəm. Zəhmət olmasa sonra yenidən cəhd edin.',
            'en': 'Sorry, I cannot process your query right now. Please try again later.'
        }
        return messages.get(language, messages['tr'])

    def _get_no_restaurant_message(self, language: str) -> str:
        """Get no restaurant found message"""
        messages = {
            'tr': 'Belirttiğiniz restoran bulunamadı.',
            'az': 'Göstərdiyiniz restoran tapılmadı.',
            'en': 'The specified restaurant was not found.'
        }
        return messages.get(language, messages['tr'])

    def _format_hours_response(self, restaurant_name: str, day: str, open_time: str,
                               close_time: str, language: str) -> str:
        """Format working hours response"""
        if language == 'tr':
            return f"{restaurant_name} bugün ({day}) {open_time} - {close_time} saatleri arasında açık."
        elif language == 'az':
            return f"{restaurant_name} bu gün ({day}) {open_time} - {close_time} saatları arasında açıqdır."
        else:
            return f"{restaurant_name} is open today ({day}) from {open_time} to {close_time}."

    def _format_closed_response(self, restaurant_name: str, day: str, language: str) -> str:
        """Format closed restaurant response"""
        if language == 'tr':
            return f"{restaurant_name} bugün ({day}) kapalı."
        elif language == 'az':
            return f"{restaurant_name} bu gün ({day}) bağlıdır."
        else:
            return f"{restaurant_name} is closed today ({day})."
